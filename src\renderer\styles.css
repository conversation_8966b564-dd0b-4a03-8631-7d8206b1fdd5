/* Bryzos Connectivity Troubleshooter Styles */

:root {
  /* Color Palette */
  --rich-black: #0d1b2aff;
  --oxford-blue: #1b263bff;
  --yinmn-blue: #415a77ff;
  --silver-lake-blue: #778da9ff;
  --platinum: #e0e1ddff;

  /* Status Colors */
  --success-green: #28a745;
  --warning-yellow: #ffc107;
  --error-red: #dc3545;
  --info-blue: var(--yinmn-blue);

  /* Typography */
  --font-family: 'Roboto', sans-serif;
  --font-size-small: 0.875rem;
  --font-size-normal: 1rem;
  --font-size-large: 1.25rem;
  --font-size-xl: 1.5rem;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  /* Border Radius */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;

  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(13, 27, 42, 0.1);
  --shadow-md: 0 4px 6px rgba(13, 27, 42, 0.15);
  --shadow-lg: 0 10px 15px rgba(13, 27, 42, 0.2);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  background-color: var(--platinum);
  color: var(--rich-black);
  line-height: 1.6;
  overflow-x: hidden;
}

/* Layout Components */
.app-container {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, var(--platinum) 0%, #f5f5f5 100%);
}

.sidebar {
  width: 280px;
  background: linear-gradient(
    180deg,
    var(--rich-black) 0%,
    var(--oxford-blue) 100%
  );
  color: var(--platinum);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-lg);
}

.main-content {
  flex: 1;
  padding: var(--spacing-xl);
  overflow-y: auto;
}

/* Header */
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 2px solid var(--silver-lake-blue);
}

.app-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--rich-black);
  margin: 0;
}

.app-subtitle {
  font-size: var(--font-size-normal);
  color: var(--yinmn-blue);
  margin-top: var(--spacing-xs);
}

/* Navigation */
.nav-menu {
  list-style: none;
  margin-bottom: var(--spacing-xl);
}

.nav-item {
  margin-bottom: var(--spacing-sm);
}

.nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  color: var(--platinum);
  text-decoration: none;
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
  font-weight: 500;
}

.nav-link:hover {
  background-color: var(--yinmn-blue);
  transform: translateX(4px);
}

.nav-link.active {
  background-color: var(--silver-lake-blue);
  color: var(--rich-black);
}

.nav-icon {
  width: 20px;
  height: 20px;
  margin-right: var(--spacing-md);
  opacity: 0.8;
}

/* Status Cards */
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.status-card {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border-left: 4px solid var(--info-blue);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.status-card.success {
  border-left-color: var(--success-green);
}

.status-card.warning {
  border-left-color: var(--warning-yellow);
}

.status-card.error {
  border-left-color: var(--error-red);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.status-title {
  font-size: var(--font-size-large);
  font-weight: 600;
  color: var(--rich-black);
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--silver-lake-blue);
}

.status-indicator.success {
  background-color: var(--success-green);
}

.status-indicator.warning {
  background-color: var(--warning-yellow);
}

.status-indicator.error {
  background-color: var(--error-red);
}

.status-content {
  color: var(--yinmn-blue);
  font-size: var(--font-size-normal);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-normal);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(
    135deg,
    var(--yinmn-blue) 0%,
    var(--silver-lake-blue) 100%
  );
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(
    135deg,
    var(--oxford-blue) 0%,
    var(--yinmn-blue) 100%
  );
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--platinum);
  color: var(--rich-black);
  border: 2px solid var(--silver-lake-blue);
}

.btn-secondary:hover {
  background: var(--silver-lake-blue);
  color: white;
}

.btn-success {
  background: var(--success-green);
  color: white;
}

.btn-warning {
  background: var(--warning-yellow);
  color: var(--rich-black);
}

.btn-danger {
  background: var(--error-red);
  color: white;
}

/* Forms */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  color: var(--rich-black);
}

.form-input {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--silver-lake-blue);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-normal);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--yinmn-blue);
  box-shadow: 0 0 0 3px rgba(65, 90, 119, 0.1);
}

/* Test Results */
.test-results {
  background: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.test-results-header {
  background: var(--rich-black);
  color: var(--platinum);
  padding: 6px 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.test-results-body {
  padding: var(--spacing-lg);
}

.test-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spacing-md);
  border-bottom: 1px solid #eee;
}

.test-item:last-child {
  border-bottom: none;
}

.test-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.test-id {
  font-size: var(--font-size-small);
  color: var(--silver-lake-blue);
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.test-name {
  font-weight: 500;
  color: var(--rich-black);
}

.test-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.test-status-text {
  font-size: var(--font-size-small);
  font-weight: 500;
}

.test-status-text.success {
  color: var(--success-green);
}

.test-status-text.warning {
  color: var(--warning-yellow);
}

.test-status-text.error {
  color: var(--error-red);
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 12px;
  background-color: #f0f0f0;
  border-radius: 6px;
  overflow: hidden;
  margin: var(--spacing-lg) 0 var(--spacing-md) 0;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--yinmn-blue) 0%,
    var(--silver-lake-blue) 50%,
    var(--yinmn-blue) 100%
  );
  background-size: 200% 100%;
  width: 0%;
  border-radius: 6px;
  transition: width 0.5s ease;
  animation: progressShimmer 2s ease-in-out infinite;
}

@keyframes progressShimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-success {
  color: var(--success-green);
}

.text-warning {
  color: var(--warning-yellow);
}

.text-error {
  color: var(--error-red);
}

.mb-0 {
  margin-bottom: 0;
}
.mb-1 {
  margin-bottom: var(--spacing-xs);
}
.mb-2 {
  margin-bottom: var(--spacing-sm);
}
.mb-3 {
  margin-bottom: var(--spacing-md);
}
.mb-4 {
  margin-bottom: var(--spacing-lg);
}

.mt-0 {
  margin-top: 0;
}
.mt-1 {
  margin-top: var(--spacing-xs);
}
.mt-2 {
  margin-top: var(--spacing-sm);
}
.mt-3 {
  margin-top: var(--spacing-md);
}
.mt-4 {
  margin-top: var(--spacing-lg);
}

/* Animation */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.spinning {
  animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
  }

  .status-grid {
    grid-template-columns: 1fr;
  }
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(13, 27, 42, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.modal-content {
  background: white;
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 20px 60px rgba(13, 27, 42, 0.3);
  min-width: 450px;
  max-width: 500px;
  width: 90%;
  text-align: center;
  animation: modalFadeIn 0.3s ease-out;
}

.modal-content h3 {
  color: var(--rich-black);
  margin-bottom: var(--spacing-lg);
  font-size: var(--font-size-large);
  font-weight: 600;
}

.modal-content p {
  color: var(--silver-lake-blue);
  font-size: var(--font-size-base);
  margin-top: var(--spacing-md);
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Button Groups */
.btn-group {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.btn-group .btn {
  flex: 1;
  min-width: 140px;
}

/* Content Sections */
.content-section {
  display: none;
}

.content-section.active {
  display: block;
}

/* Sidebar Footer */
.sidebar-footer {
  margin-top: auto;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--yinmn-blue);
}

.sidebar-footer .btn {
  width: 100%;
}

/* Textarea Styling */
textarea.form-input {
  resize: vertical;
  min-height: 120px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: var(--font-size-small);
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.btn.loading {
  position: relative;
}

.btn.loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  margin: auto;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s ease infinite;
}

/* Button Groups */
.btn-group {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.btn-group .btn {
  flex: 1;
  min-width: 140px;
}

/* Content Sections */
.content-section {
  display: none;
}

.content-section.active {
  display: block;
}

/* Additional Form Styles */
textarea.form-input {
  resize: vertical;
  min-height: 120px;
  font-family: monospace;
}

/* Sidebar Footer */
.sidebar-footer {
  margin-top: auto;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--yinmn-blue);
}

.sidebar-footer .btn {
  width: 100%;
}

/* Certificate Details */
.certificate-details {
  background: #f8f9fa;
  padding: var(--spacing-md);
  margin-top: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  border-left: 3px solid var(--yinmn-blue);
}

.certificate-details p {
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-small);
}

.certificate-details strong {
  color: var(--rich-black);
}

/* Test Error Messages */
.test-error {
  background: #ffe6e6;
  color: var(--error-red);
  padding: var(--spacing-sm);
  margin-top: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-small);
  border-left: 3px solid var(--error-red);
}

/* System Information */
.system-info h4 {
  color: var(--rich-black);
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-large);
  border-bottom: 2px solid var(--silver-lake-blue);
  padding-bottom: var(--spacing-sm);
}

.system-info h4:first-child {
  margin-top: 0;
}

.system-info h5 {
  color: var(--yinmn-blue);
  margin-top: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
}

.interface-group {
  margin-bottom: var(--spacing-lg);
  background: #f8f9fa;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
}

.interface-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  border-bottom: 1px solid #e9ecef;
  font-family: monospace;
  font-size: var(--font-size-small);
}

.interface-item:last-child {
  border-bottom: none;
}

/* Loading States */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  color: var(--yinmn-blue);
}

.loading::before {
  content: '⟳';
  margin-right: var(--spacing-sm);
  animation: spin 1s linear infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    width: 100%;
    margin-bottom: var(--spacing-sm);
  }

  .status-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .app-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .modal-content {
    margin: var(--spacing-md);
    width: calc(100% - 2rem);
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  :root {
    --platinum: #2d3748;
    --rich-black: #e2e8f0;
    --oxford-blue: #4a5568;
    --yinmn-blue: #718096;
    --silver-lake-blue: #a0aec0;
  }

  .status-card {
    background: #374151;
    color: var(--rich-black);
  }

  .test-results {
    background: #374151;
  }

  .form-input {
    background: #4a5568;
    color: var(--rich-black);
    border-color: var(--yinmn-blue);
  }

  .certificate-details {
    background: #4a5568;
  }

  .interface-group {
    background: #4a5568;
  }
}
